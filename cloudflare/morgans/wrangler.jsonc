/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "morgans",
  "main": "src/index.ts",
  "compatibility_date": "2025-08-16",
  "observability": {
    "enabled": true
  },
  "vars": {
    "ENVIRONMENT": "local",
    "FF_CONNECTIONS": true,
    "SF_MAINTENANCE_MODE": false
  },
  "env": {
    "dev": {
      "vars": {
        "ENVIRONMENT": "dev",
        "FF_CONNECTIONS": true,
        "SF_MAINTENANCE_MODE": false
      },
      "routes": [
        {
          "pattern": "dev-app.kazeel.com/v1/feature_flags*",
          "custom_domain": false
        },
        {
          "pattern": "app.kazeel.com/web/v1/feature_flags*",
          "custom_domain": false
        }
      ]
    },
    "prod": {
      "vars": {
        "ENVIRONMENT": "prod",
        "FF_CONNECTIONS": false,
        "SF_MAINTENANCE_MODE": false
      },
      "routes": [
        {
          "pattern": "app.kazeel.com/v1/feature_flags*",
          "custom_domain": false
        },
        {
          "pattern": "app.kazeel.com/web/v1/feature_flags*",
          "custom_domain": false
        }
      ]
    }
  }
}
