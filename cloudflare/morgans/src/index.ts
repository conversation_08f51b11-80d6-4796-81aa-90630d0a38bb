import { Context, Hono } from "hono";
import { trimTrailingSlash } from "hono/trailing-slash";
import { getConfig } from "./config";

const app = new Hono<{ Bindings: Env }>()
  .use(trimTrailingSlash())
  .basePath("/v1");

const ConfigHandler = (c: Context<{ Bindings: Env }>) => {
  console.log("aa");
  const configurationObj = getConfig(c.env);
  return c.json(configurationObj);
};
app.get("/config", ConfigHandler).get("/web/config", ConfigHandler);

export default app;
