/**
 * Represents a feature flag with optional sub-features.
 */
export interface FeatureFlag {
  enabled: boolean;
  subFeatures?: Record<string, boolean>;
}

/**
 * Feature flags for the application.
 */
export interface FeatureFlags {
  connections: FeatureFlag;
}

/**
 * System-level flags.
 */
export interface SystemFlags {
  maintenanceMode: boolean;
}

/**
 * Application configuration structure.
 */
export interface AppConfig {
  features: FeatureFlags;
  system: SystemFlags;
}

/**
 * Actual Configuration Response
 */
export const getConfig = (env: Env): AppConfig => ({
  features: {
    connections: {
      enabled: env.FF_CONNECTIONS,
    },
  },
  system: {
    maintenanceMode: env.SF_MAINTENANCE_MODE,
  },
});
