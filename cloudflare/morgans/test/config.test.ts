import { SELF } from "cloudflare:test";
import { describe, expect, it } from "vitest";
import { config, type AppConfig } from "../src/config";

describe("Morgans Worker Test", () => {
    it("should return a valid config", async () => {
        const res = await SELF.fetch("http://localhost/v1/config");
        const data = (await res.json()) as AppConfig;


        expect(res.status).toBe(200);
        expect(data.features.connections.enabled).toBe(config.features.connections.enabled);
        expect(data.system.maintenanceMode).toBe(config.system.maintenanceMode);
    });
});
