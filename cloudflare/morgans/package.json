{"name": "morgans", "type": "module", "private": true, "version": "0.0.1", "scripts": {"dev": "vite dev", "dev:dev": "CLOUDFLARE_ENV=dev vite dev", "dev:prod": "CLOUDFLARE_ENV=prod vite dev", "build": "vite build", "preview": "npm run build && vite preview", "deploy": "npm run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "test": "vitest"}, "dependencies": {"hono": "^4.9.2"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.11.7", "@cloudflare/vitest-pool-workers": "^0.8.65", "vite": "^7.1.3", "vitest": "~3.2.0", "wrangler": "^4.32.0"}}